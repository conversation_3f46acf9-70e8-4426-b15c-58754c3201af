{"name": "oauth2-auth-flow", "version": "1.0.0", "description": "OAuth2 authentication flow implementation with node-oidc-provider", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "client": "tsx watch src/client.ts"}, "keywords": ["oauth2", "oidc", "authentication", "hono", "typescript"], "author": "", "license": "MIT", "dependencies": {"hono": "^3.12.0", "oidc-provider": "^8.4.6", "crypto": "^1.0.1"}, "devDependencies": {"@types/node": "^20.10.0", "tsx": "^4.6.0", "typescript": "^5.3.0"}}