import { Adapter, AdapterPayload } from 'oidc-provider';

// In-memory storage adapter for demo purposes
// In production, use a proper database
class MemoryAdapter implements Adapter {
  private storage = new Map<string, AdapterPayload>();
  private userCodes = new Map<string, string>();
  private uids = new Map<string, string>();

  constructor(private name: string) {}

  async upsert(id: string, payload: AdapterPayload, expiresIn?: number): Promise<void> {
    const key = this.key(id);
    
    if (payload.userCode) {
      this.userCodes.set(payload.userCode, id);
    }

    if (payload.uid) {
      this.uids.set(payload.uid, id);
    }

    this.storage.set(key, {
      ...payload,
      ...(expiresIn && { exp: Date.now() + (expiresIn * 1000) })
    });
  }

  async find(id: string): Promise<AdapterPayload | undefined> {
    const key = this.key(id);
    const payload = this.storage.get(key);
    
    if (!payload) return undefined;
    
    // Check expiration
    if (payload.exp && payload.exp < Date.now()) {
      this.storage.delete(key);
      return undefined;
    }
    
    return payload;
  }

  async findByUserCode(userCode: string): Promise<AdapterPayload | undefined> {
    const id = this.userCodes.get(userCode);
    return id ? this.find(id) : undefined;
  }

  async findByUid(uid: string): Promise<AdapterPayload | undefined> {
    const id = this.uids.get(uid);
    return id ? this.find(id) : undefined;
  }

  async consume(id: string): Promise<void> {
    const payload = await this.find(id);
    if (payload) {
      payload.consumed = Math.floor(Date.now() / 1000);
      await this.upsert(id, payload);
    }
  }

  async destroy(id: string): Promise<void> {
    const key = this.key(id);
    const payload = this.storage.get(key);
    
    if (payload?.userCode) {
      this.userCodes.delete(payload.userCode);
    }
    
    if (payload?.uid) {
      this.uids.delete(payload.uid);
    }
    
    this.storage.delete(key);
  }

  async revokeByGrantId(grantId: string): Promise<void> {
    const keys = Array.from(this.storage.keys());
    for (const key of keys) {
      const payload = this.storage.get(key);
      if (payload?.grantId === grantId) {
        this.storage.delete(key);
      }
    }
  }

  private key(id: string): string {
    return `${this.name}:${id}`;
  }
}

export default MemoryAdapter;
