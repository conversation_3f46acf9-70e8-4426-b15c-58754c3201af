import crypto from 'crypto';

// Configuration constants
export const CONFIG = {
  PORT: 3000,
  CLIENT_PORT: 3001,
  ISSUER: 'http://localhost:3000',
  CLIENT_REDIRECT_URI: 'http://localhost:3001/callback',
  HMAC_SECRET: 'your-super-secret-hmac-key-change-in-production',
  SESSION_SECRET: 'your-session-secret-change-in-production'
};

// Generate random client ID
export function generateClientId(): string {
  return crypto.randomBytes(16).toString('hex');
}

// Generate HMAC-based client secret
export function generateClientSecret(clientId: string): string {
  return crypto
    .createHmac('sha256', CONFIG.HMAC_SECRET)
    .update(clientId)
    .digest('hex');
}

// In-memory storage for demo purposes
export interface Client {
  client_id: string;
  client_secret: string;
  redirect_uris: string[];
  grant_types: string[];
  response_types: string[];
}

export interface User {
  id: string;
  username: string;
  password: string;
}

// Demo client and user data
export const DEMO_CLIENT_ID = generateClientId();
export const DEMO_CLIENT_SECRET = generateClientSecret(DEMO_CLIENT_ID);

export const clients: Client[] = [
  {
    client_id: DEMO_CLIENT_ID,
    client_secret: DEMO_CLIENT_SECRET,
    redirect_uris: [CONFIG.CLIENT_REDIRECT_URI],
    grant_types: ['authorization_code', 'refresh_token'],
    response_types: ['code']
  }
];

export const users: User[] = [
  {
    id: '1',
    username: 'demo',
    password: 'password123'
  }
];

console.log('Demo Client Credentials:');
console.log('Client ID:', DEMO_CLIENT_ID);
console.log('Client Secret:', DEMO_CLIENT_SECRET);
